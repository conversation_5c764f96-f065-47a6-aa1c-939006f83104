using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDeviceDal : EfEntityRepositoryBase<UserDevice, GymContext>, IUserDeviceDal
    {
        // Constructor injection (Scalability i�in)
        public EfUserDeviceDal(GymContext context) : base(context)
        {
        }

        public List<UserDevice> GetActiveDevicesByUserId(int userId)
        {
            return _context.UserDevices
                .Where(d => d.UserId == userId && d.IsActive == true)
                .ToList();
        }

        public UserDevice GetByRefreshToken(string refreshToken)
        {
            return _context.UserDevices
                .FirstOrDefault(d => d.RefreshToken == refreshToken && d.IsActive == true);
        }

        public IResult AddDeviceWithManagement(UserDevice device)
        {
            if (_context != null)
            {
                // DI kullan�l�yor - Scalability optimized
                const int MAX_ACTIVE_DEVICES = 5; // Web i�in
                const int MAX_MOBILE_DEVICES = 1; // Mobil i�in sadece 1 cihaz

                CleanExpiredTokens();

                var activeDevices = _context.UserDevices
                    .Where(d => d.UserId == device.UserId && d.IsActive == true)
                    .ToList();

                // Mobil cihaz kontrol� (DeviceInfo'da "Mobile" ge�iyorsa mobil)
                bool isMobileDevice = device.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                                    device.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                                    device.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                                    device.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true;

                if (isMobileDevice)
                {
                    // Mobil i�in: T�m eski mobil cihazlar� revoke et (sadece 1 mobil cihaz)
                    var existingMobileDevices = activeDevices.Where(d =>
                        d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                        d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                        d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                        d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true).ToList();

                    foreach (var existingDevice in existingMobileDevices)
                    {
                        existingDevice.IsActive = false;
                        existingDevice.RefreshToken = null;
                        existingDevice.RefreshTokenExpiration = null;
                        _context.UserDevices.Update(existingDevice);
                    }
                }
                else
                {
                    // Web i�in: Maksimum cihaz say�s� kontrol�
                    var webDevices = activeDevices.Where(d =>
                        !(d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                          d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                          d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                          d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true)).ToList();

                    if (webDevices.Count >= MAX_ACTIVE_DEVICES)
                    {
                        var oldestDevice = webDevices
                            .OrderBy(d => d.CreationDate)
                            .First();
                        oldestDevice.IsActive = false;
                        oldestDevice.RefreshToken = null;
                        oldestDevice.RefreshTokenExpiration = null;
                        _context.UserDevices.Update(oldestDevice);
                    }
                }

                _context.UserDevices.Add(device);
                _context.SaveChanges();
                return new SuccessResult();
            }

            // DI kullan�lm�yorsa exception f�rlat (art�k backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public void CleanExpiredTokens()
        {
            if (_context != null)
            {
                // DI kullan�l�yor - Scalability optimized
                var expiredDevices = _context.UserDevices.Where(d =>
                    d.IsActive == true &&
                    d.RefreshTokenExpiration.HasValue &&
                    d.RefreshTokenExpiration.Value < DateTime.Now).ToList();

                foreach (var device in expiredDevices)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    _context.UserDevices.Update(device);
                }

                if (expiredDevices.Any())
                {
                    _context.SaveChanges();
                }
                return;
            }

            // DI kullan�lm�yorsa exception f�rlat (art�k backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public IResult RevokeAllDevicesExceptCurrent(int userId, string currentRefreshToken)
        {
            if (_context != null)
            {
                // DI kullan�l�yor - Scalability optimized
                var devices = _context.UserDevices.Where(d =>
                    d.UserId == userId &&
                    d.IsActive == true &&
                    d.RefreshToken != currentRefreshToken).ToList();

                foreach (var device in devices)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    _context.UserDevices.Update(device);
                }

                if (devices.Any())
                {
                    _context.SaveChanges();
                }

                return new SuccessResult();
            }

            // DI kullan�lm�yorsa exception f�rlat (art�k backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public IDataResult<UserDevice> GetByRefreshTokenWithValidation(string refreshToken)
        {
            if (_context != null)
            {
                // DI kullan�l�yor - Scalability optimized
                var device = _context.UserDevices
                    .FirstOrDefault(d => d.RefreshToken == refreshToken && d.IsActive == true);

                if (device == null)
                    return new ErrorDataResult<UserDevice>("Ge�ersiz refresh token");

                // Sadece kontrol edilen token'�n s�resini kontrol et
                if (device.RefreshTokenExpiration.HasValue && device.RefreshTokenExpiration.Value < DateTime.Now)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    _context.UserDevices.Update(device);
                    _context.SaveChanges();
                    return new ErrorDataResult<UserDevice>("S�resi dolmu� refresh token");
                }

                return new SuccessDataResult<UserDevice>(device);
            }

            // DI kullan�lm�yorsa exception f�rlat (art�k backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation ve entity manipulation logic DAL katman�nda
        /// </summary>
        public IResult RevokeDeviceWithValidation(int deviceId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullan�l�yor - Scalability optimized
                    var device = _context.UserDevices.FirstOrDefault(d => d.Id == deviceId);
                    if (device == null)
                    {
                        return new ErrorResult("Cihaz bulunamad�");
                    }

                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;

                    _context.SaveChanges();
                    return new SuccessResult();
                }

                // DI kullan�lm�yorsa exception f�rlat (art�k backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Cihaz iptal edilirken hata olu�tu: {ex.Message}");
            }
        }
    }
}
