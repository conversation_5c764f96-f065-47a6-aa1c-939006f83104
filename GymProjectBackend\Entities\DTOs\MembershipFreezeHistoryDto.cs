using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MembershipFreezeHistoryDto : IDto
    {
        public int FreezeHistoryID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public string Branch { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime PlannedEndDate { get; set; }
        public DateTime? ActualEndDate { get; set; }
        public int FreezeDays { get; set; }
        public int? UsedDays { get; set; }
        public string CancellationType { get; set; }
        public DateTime CreationDate { get; set; }
    }
}
