﻿using Core.Entities;

namespace Entities.DTOs
{
    public class UserLicenseDto : IDto
    {
        public int UserLicenseID { get; set; }
        public int UserID { get; set; }
        public string UserName { get; set; }
        public string UserEmail { get; set; }
        public string CompanyName { get; set; }
        public int LicensePackageID { get; set; }
        public string PackageName { get; set; }
        public string Role { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool? IsActive { get; set; }
    }
}
