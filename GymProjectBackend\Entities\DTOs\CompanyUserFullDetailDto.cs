using Core.Entities;
using System;

namespace Entities.DTOs
{
    /// <summary>
    /// CompanyUser'ın tüm detaylarını içeren DTO
    /// User, Company ve diğer ilişkili tablolardan bilgiler dahil
    /// </summary>
    public class CompanyUserFullDetailDto : IDto
    {
        // CompanyUser bilgileri
        public int CompanyUserID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public int CityID { get; set; }
        public int TownID { get; set; }
        public string CityName { get; set; }
        public string TownName { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        
        // User bilgileri (ilişkili User tablosundan)
        public int? UserID { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool? UserIsActive { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public bool RequirePasswordChange { get; set; }

        // Company bilgileri (UserCompany üzerinden)
        public int? CompanyID { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyPhone { get; set; }
        public string? CompanyAddress { get; set; }
        public int? CompanyCityID { get; set; }
        public int? CompanyTownID { get; set; }
        public bool? CompanyIsActive { get; set; }

        // İlişki ID'leri
        public int? CompanyAddressId { get; set; }
        public int? UserCompanyId { get; set; }
        
        // İstatistik bilgileri
        public int TotalMembers { get; set; }
        public int ActiveMembers { get; set; }
        public decimal MonthlyRevenue { get; set; }
        
        // Hesap durumu bilgileri
        public string AccountStatus => GetAccountStatus();
        public string FullDisplayName => $"{FirstName ?? ""} {LastName ?? ""}".Trim();
        
        private string GetAccountStatus()
        {
            if (!IsActive.HasValue || !IsActive.Value) return "Pasif";
            if (!UserIsActive.HasValue || !UserIsActive.Value) return "Kullanıcı Hesabı Pasif";
            if (RequirePasswordChange) return "Şifre Değiştirmeli";
            return "Aktif";
        }
    }
}
